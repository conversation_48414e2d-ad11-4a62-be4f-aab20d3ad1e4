<template>
  <div class="water-quality-monitoring-container">
    <!-- 地图容器 -->
    <div class="map-container">
      <HOpenlayersMap
        ref="mapRef"
        @register="registerMap"
        :olView="mapConfig.olView"
        :showXYZ="true"
        :olXYZ="mapConfig.olXYZ"
        :showMousePosition="true"
        class="w-full h-full"
        @changeFullScreen="changeFullScreen"
      >
        <!-- 天地图矢量注记图层 -->
        <Layers.OlTileLayer title="矢量注记">
          <Sources.OlSourceXyz crossOrigin="anonymous" :url="mapConfig.olAnnotation.url" />
        </Layers.OlTileLayer>
        <!-- 深圳市边界图层 -->
        <Layers.OlVectorLayer :zIndex="5" v-if="shenzhenBoundaryFeatures.length > 0">
          <Sources.OlSourceVector>
            <Map.OlFeature
              v-for="(feature, index) in shenzhenBoundaryFeatures"
              :key="`boundary-${index}`"
              :properties="feature.properties"
            >
              <Geometries.OlGeomMultiPolygon
                v-if="feature.geometryType === 'MultiPolygon'"
                :coordinates="feature.coordinates"
              />
              <Geometries.OlGeomPolygon
                v-else-if="feature.geometryType === 'Polygon'"
                :coordinates="feature.coordinates"
              />
            </Map.OlFeature>
          </Sources.OlSourceVector>
          <Styles.OlStyle>
            <Styles.OlStyleStroke :color="'rgba(64, 150, 255, 1)'" :width="3" />
            <Styles.OlStyleFill :color="'rgba(64, 150, 255, 0.1)'" />
          </Styles.OlStyle>
        </Layers.OlVectorLayer>
        <!-- 水质监测点位聚合图层（缩放级别>=12时显示） -->
        <Layers.OlVectorLayer :zIndex="10" v-if="!showHeatmap">
          <Sources.OlSourceCluster
            :distance="clusterDistance"
            :wrapX="false"
            :geometryFunction="geometryFunction"
          >
            <Sources.OlSourceVector>
              <Map.OlFeature
                v-for="(point, index) in currentPoints"
                :key="`${currentTimeIndex}-${index}`"
                :properties="point"
              >
                <Geometries.OlGeomPoint :coordinates="[point.longitude, point.latitude]" />
              </Map.OlFeature>
            </Sources.OlSourceVector>
          </Sources.OlSourceCluster>
          <Styles.OlStyle :overrideStyleFunction="clusterStyleFunction" />
        </Layers.OlVectorLayer>

        <!-- 水质监测热力图图层-红色（缩放级别<12时显示） -->
        <!-- :gradient="['#00f', '#0ff', '#0f0', '#ff0', '#f00']" -->
        <Layers.OlHeatmapLayer
          v-if="showHeatmap"
          :gradient="['#f00', '#f00', '#f00', '#f00']"
          :zIndex="10"
          :blur="15"
          :radius="8"
        >
          <Sources.OlSourceVector>
            <Map.OlFeature
              v-for="(point, index) in heatmapPointsRed"
              :key="`heatmap-${currentTimeIndex}-${index}`"
              :properties="{ weight: point.weight }"
            >
              <Geometries.OlGeomPoint :coordinates="[point.longitude, point.latitude]" />
            </Map.OlFeature>
          </Sources.OlSourceVector>
        </Layers.OlHeatmapLayer>
        <!-- 水质监测热力图图层-绿色（缩放级别<12时显示） -->
        <Layers.OlHeatmapLayer
          v-if="showHeatmap"
          :gradient="['#0f0', '#0f0', '#0f0', '#0f0']"
          :zIndex="10"
          :blur="15"
          :radius="8"
        >
          <Sources.OlSourceVector>
            <Map.OlFeature
              v-for="(point, index) in heatmapPointsGreen"
              :key="`heatmap-${currentTimeIndex}-${index}`"
              :properties="{ weight: point.weight }"
            >
              <Geometries.OlGeomPoint :coordinates="[point.longitude, point.latitude]" />
            </Map.OlFeature>
          </Sources.OlSourceVector>
        </Layers.OlHeatmapLayer>
      </HOpenlayersMap>
    </div>

    <!-- 播放轴控制器 -->
    <div class="playback-controller">
      <div class="controller-content">
        <!-- 播放控制按钮 -->
        <div class="play-controls">
          <Button
            :type="isPlaying ? 'default' : 'primary'"
            :icon="
              isPlaying
                ? h(Icon, { icon: 'ant-design:pause-outlined' })
                : h(Icon, { icon: 'ant-design:play-circle-outlined' })
            "
            @click="togglePlay"
            size="large"
          >
            {{ isPlaying ? '暂停' : '播放' }}
          </Button>
          <Button
            type="default"
            :icon="h(Icon, { icon: 'ant-design:reload-outlined' })"
            @click="resetPlayback"
            size="large"
          >
            重置
          </Button>
          <!-- <Button type="default" @click="testZoom" size="large">
            测试缩放({{ currentZoom.toFixed(1) }})
          </Button> -->
        </div>
        <div class="time-control">
          播放数据总数：
          <Input
            type="number"
            v-model:value="timeBreak"
            @change="setTimeBreak"
            style="width: 80px; height: 40px; margin-right: 5px" />
          播放时间(s)：
          <Input
            type="number"
            v-model:value="timePlayTotal"
            @change="setTimePlayTotal"
            style="width: 80px; height: 40px; margin-left: 5px; margin-right: 5px"
        /></div>
        <!-- 时间显示 -->
        <div class="time-display" style="display: none">
          <div class="current-time">{{ currentTimeDisplay }}</div>
          <!-- <div class="time-info">{{
            `第 ${currentTimeIndex + 1} 组数据 / 共 ${totalTimeStep} 组`
          }}</div> -->
        </div>

        <!-- 进度条 -->
        <div class="progress-container">
          <Slider
            v-model:value="currentTimeIndex"
            :min="0"
            :max="timeBreak - 1"
            :step="1"
            @change="onProgressChange"
            :tooltip-formatter="formatTooltip"
            class="progress-slider"
          />
        </div>

        <!-- 播放速度控制 -->
        <!-- <div class="speed-controls">
          <span>播放速度：</span>
          <Select v-model:value="playbackSpeed" style="width: 100px" @change="onSpeedChange">
            <SelectOption :value="0.5">0.5x</SelectOption>
            <SelectOption :value="1">1x</SelectOption>
            <SelectOption :value="2">2x</SelectOption>
            <SelectOption :value="4">4x</SelectOption>
          </Select>
        </div> -->

        <!-- 聚合距离控制 -->
        <!-- <div class="cluster-controls">
          <span>聚合距离：</span>
          <Slider
            v-model:value="clusterDistance"
            :min="20"
            :max="100"
            :step="10"
            style="width: 120px"
            :tooltip-formatter="(value) => `${value}px`"
          />
        </div> -->

        <!-- 数据统计 -->
        <!-- <div class="data-stats">
           <div class="stat-item">
            <span class="stat-label">绿色点位：</span>
            <span class="stat-value">{{ greenPointsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">黄色点位：</span>
            <span class="stat-value">{{ yellowPointsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">红色点位：</span>
            <span class="stat-value">{{ redPointsCount }}</span>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, onUnmounted, watch, h, nextTick } from 'vue';
  import {
    Button,
    Slider,
    Input,
    // Select, SelectOption
  } from 'ant-design-vue';
  import { HOpenlayersMap } from '/@/components/HMap';
  import { Map, Layers, Sources, Geometries, Styles } from '/@/components/HMap/openlayer';
  import type { HMapInstance } from '/@/components/HMap';
  import { Icon } from '/@/components/Icon';
  import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
  import { useUserStoreWithOut } from '/@/store/modules/user';
  import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style';
  import type { Feature } from 'ol';
  import type { Point } from 'ol/geom';
  import shenzhen from './440300.json';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  // 接口定义
  interface WaterQualityPoint {
    id: string;
    longitude: number;
    latitude: number;
    type: 'type1' | 'type2';
    color: string;
    value: number;
    timestamp: string;
  }

  interface TimeSlotData {
    timestamp: string;
    points: WaterQualityPoint[];
  }

  // 响应式数据
  const mapRef = ref<InstanceType<typeof HOpenlayersMap>>();
  const mapInstance = ref<HMapInstance>();

  // 播放控制
  const isPlaying = ref(false);
  const currentTimeIndex = ref(0);
  const playbackSpeed = ref(1);
  const playTimer = ref<NodeJS.Timeout>();
  // 播放数据总数
  const timeBreak = ref(100);
  // 播放总耗时
  const timePlayTotal = ref(10);

  // 地图缩放级别和热力图显示控制
  const currentZoom = ref(13);
  const showHeatmap = computed(() => {
    // console.log('currentZoom.value', currentZoom.value);
    return currentZoom.value < 12;
  });
  // console.log('showHeatmap', showHeatmap);
  // 地图配置
  const mapConfig = reactive({
    olView: {
      // center: [114.0579, 22.5431], // 深圳市中心坐标
      center: [114.3, 22.67],
      zoom: 13,
      projection: 'EPSG:4326',
      enableRotation: true,
    },
    olXYZ: {
      url: 'http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=75d8c14a258f281e1a818403a365cf78',
      // url: 'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=75d8c14a258f281e1a818403a365cf78',
    },
    // 矢量注记图层配置
    olAnnotation: {
      url: 'http://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=75d8c14a258f281e1a818403a365cf78',
    },
  });
  const setTimeBreak = () => {
    console.log('watch-timeBeak', timeBreak.value);
    if (timeBreak.value <= 0) {
      timeBreak.value = 1; // 最小值为1
    }
  };
  const setTimePlayTotal = () => {
    if (timePlayTotal.value <= 0) {
      timePlayTotal.value = 1; // 最小值为1
    }
  };
  // 深圳市边界数据配置
  const shenzhenBoundaryFeatures = ref<any[]>([]);

  // 加载深圳市边界数据
  const loadShenzhenBoundary = async () => {
    try {
      // https://geo.datav.aliyun.com/areas_v3/bound/440300_full.json
      // https://geojson.cn/api/china/1.6.2/440000/440300.json
      // const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/440300_full.json');
      // const geoJsonData = await response.json();

      if (shenzhen.features) {
        shenzhenBoundaryFeatures.value = shenzhen.features.map((feature: any) => ({
          properties: feature.properties,
          coordinates: feature.geometry.coordinates,
          geometryType: feature.geometry.type,
        }));
        console.log('深圳边界数据加载成功:', shenzhenBoundaryFeatures.value);
      }
    } catch (error) {
      console.error('加载深圳边界数据失败:', error);
    }
  };

  // 聚合配置
  const clusterDistance = ref(10); // 聚合距离，像素单位

  // 几何函数，用于聚合
  const geometryFunction = (feature: Feature<Point>): Point => {
    return feature.getGeometry() as Point;
  };

  // 聚合样式函数
  const clusterStyleFunction = (feature: Feature, _currentStyle: Style, _resolution: number) => {
    const clusteredFeatures = feature.get('features');
    const size = clusteredFeatures.length;

    if (size === 1) {
      // 单个点位的样式
      const point = clusteredFeatures[0].getProperties() as WaterQualityPoint;
      return new Style({
        image: new CircleStyle({
          radius: 8,
          fill: new Fill({
            color: point.color,
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 2,
          }),
        }),
      });
    } else {
      // 聚合点的样式
      const yellowCount = clusteredFeatures.filter(
        (f: Feature) => f.getProperties().color === '#FFD700',
      ).length;
      const redCount = clusteredFeatures.filter(
        (f: Feature) => f.getProperties().color === '#FF4444',
      ).length;
      const greenCount = clusteredFeatures.filter(
        (f: Feature) => f.getProperties().color === '#00FF00',
      ).length;
      // 根据主要类型确定聚合点颜色
      const mainColor =
        yellowCount > redCount && yellowCount > greenCount
          ? '#FFD700'
          : redCount > greenCount
          ? '#FF4444'
          : '#00FF00';
      const radius = Math.min(Math.max(12, size * 2), 30); // 根据数量调整大小

      return new Style({
        image: new CircleStyle({
          radius: radius,
          fill: new Fill({
            color: mainColor + '80', // 半透明
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 3,
          }),
        }),
        text: new Text({
          text: size.toString(),
          font: 'bold 12px Arial',
          fill: new Fill({
            color: '#ffffff',
          }),
          stroke: new Stroke({
            color: mainColor,
            width: 2,
          }),
        }),
      });
    }
  };

  // 数据存储
  const allTimeSlots = ref<TimeSlotData[]>([]);
  // 数据step
  const totalTimeStep = computed(() => Math.floor(allTimeSlots.value.length / timeBreak.value));

  // 当前显示的点位数据
  const currentPoints = computed(() => {
    if (allTimeSlots.value.length === 0 || currentTimeIndex.value >= timeBreak.value) {
      return [];
    }
    return allTimeSlots.value[Math.floor(currentTimeIndex.value * totalTimeStep.value)].points;
  });

  // 热力图数据-红色
  const heatmapPointsRed = computed(() => {
    if (allTimeSlots.value.length === 0) {
      return [];
    }
    let points: any[] = [];
    // const totalSlots = Math.floor(allTimeSlots.value.length / timeBreak.value);
    const currentSlot = currentTimeIndex.value;
    const slotData = allTimeSlots.value[Math.floor(currentSlot * totalTimeStep.value)];
    if (slotData) {
      slotData.points.forEach((point) => {
        if (point.color === '#FF4444') {
          points.push({
            longitude: point.longitude,
            latitude: point.latitude,
            weight: 0.5,
            // weight: point.value / 100, // 将值标准化为0-1之间的权重
          });
        }
      });
    }
    // console.log('heat-points', points);
    return points;
  });
  // 热力图数据-绿色
  const heatmapPointsGreen = computed(() => {
    if (allTimeSlots.value.length === 0) {
      return [];
    }
    let points: any[] = [];
    const currentSlot = currentTimeIndex.value;
    const slotData = allTimeSlots.value[Math.floor(currentSlot * totalTimeStep.value)];
    if (slotData) {
      slotData.points.forEach((point) => {
        if (point.color === '#00FF00') {
          points.push({
            longitude: point.longitude,
            latitude: point.latitude,
            weight: 0.5,
            // weight: point.value / 100, // 将值标准化为0-1之间的权重
          });
        }
      });
    }
    return points;
  });
  // 热力图数据-黄色
  // const heatmapPointsYellow = computed(() => {
  //   if (allTimeSlots.value.length === 0) {
  //     return [];
  //   }
  //   let points: any[] = [];
  //   const currentSlot = currentTimeIndex.value;
  //  const slotData = allTimeSlots.value[Math.floor(currentSlot * totalTimeStep.value)];
  //   if (slotData) {
  //     slotData.points.forEach((point) => {
  //       if (point.color === '#FFD700') {
  //         points.push({
  //           longitude: point.longitude,
  //           latitude: point.latitude,
  //           weight: 0.5,
  //           // weight: point.value / 100, // 将值标准化为0-1之间的权重
  //         });
  //       }
  //     });
  //   }
  //   return points;
  // });
  // 当前时间显示
  const currentTimeDisplay = computed(() => {
    if (allTimeSlots.value.length === 0 || currentTimeIndex.value >= timeBreak.value) {
      return '00:00:00';
    }
    return allTimeSlots.value[Math.floor(currentTimeIndex.value * totalTimeStep.value)].timestamp;
  });

  // 点位统计
  // const yellowPointsCount = computed(() => {
  //   return currentPoints.value.filter((point) => point.color === '#FFD700').length;
  // });

  // const redPointsCount = computed(() => {
  //   return currentPoints.value.filter((point) => point.color === '#FF4444').length;
  // });
  // const greenPointsCount = computed(() => {
  //   return currentPoints.value.filter((point) => point.color === '#00FF00').length;
  // });
  const getConentBody = ref<boolean>(false);
  const { setHeaderHeight, getOldHeightRef } = useLayoutHeight();
  const userStore = useUserStoreWithOut();
  // 全屏控件
  const changeFullScreen = (fullscreen) => {
    getConentBody.value = fullscreen;
    if (!fullscreen) {
      const height = getOldHeightRef();
      setHeaderHeight(height);
      userStore.setIsFullFooterWidth(false);
    } else {
      setHeaderHeight(0);
      userStore.setIsFullFooterWidth(true);
    }
  };
  // 生成深圳市范围内的随机坐标
  const generateRandomCoordinate = (): [number, number] => {
    // 深圳市大致范围：经度 113.75-114.65，纬度 22.45-22.86
    const minLng = 114.25;
    const maxLng = 114.35;
    const minLat = 22.66;
    const maxLat = 22.72;

    const lng = minLng + Math.random() * (maxLng - minLng);
    const lat = minLat + Math.random() * (maxLat - minLat);

    return [lng, lat];
  };
  // // total和经度分成十等份 从时间和经度上 从绿到红
  const getcolor = (total, i, lng) => {
    const minLng = 114.25;
    const Magnification = total / 10;
    // console.log('total, i, lng, Magnification', total, i, lng, Magnification);
    let color = '00FF00';

    // 第十个时间段
    if (total > i) lng - minLng < 0.09 ? (color = '#FF4444') : (color = '#00FF00');
    // 第九个时间段
    if (9 * Magnification > i) lng - minLng < 0.08 ? (color = '#FF4444') : (color = '#00FF00');
    // 第八个时间段
    if (8 * Magnification > i) lng - minLng < 0.07 ? (color = '#FF4444') : (color = '#00FF00');
    // 第七个时间段
    if (7 * Magnification > i) lng - minLng < 0.06 ? (color = '#FF4444') : (color = '#00FF00');
    // 第六个时间段
    if (6 * Magnification > i) lng - minLng < 0.05 ? (color = '#FF4444') : (color = '#00FF00');
    // 第五个时间段
    if (5 * Magnification > i) lng - minLng < 0.04 ? (color = '#FF4444') : (color = '#00FF00');
    // 第四个时间段
    if (4 * Magnification > i) lng - minLng < 0.03 ? (color = '#FF4444') : (color = '#00FF00');
    // 第三个时间段
    if (3 * Magnification > i) lng - minLng < 0.02 ? (color = '#FF4444') : (color = '#00FF00');
    // 第二个时间段
    if (2 * Magnification > i) lng - minLng < 0.01 ? (color = '#FF4444') : (color = '#00FF00');
    // 第一个时间段
    if (Magnification > i) color = '#00FF00';

    return color;
  };
  // 生成24小时的模拟数据
  const generateTimeSlotData = (): TimeSlotData[] => {
    const timeSlots: TimeSlotData[] = [];
    const startTime = new Date();
    const total = 0 + startTime.getHours() * 60 + startTime.getMinutes();
    startTime.setHours(0, 0, 0, 0); // 从今天00:00开始，00:00:00
    // 随机生成80个经纬度点位
    const lngLat: [number, number][] = [];
    for (let i = 0; i < 80; i++) {
      lngLat.push(generateRandomCoordinate());
    }
    // 定义三种预选颜色
    // const colorType = ['#00FF00', '#FFD700', '#FF4444'];
    // 每1分钟一组数据，24小时共24*60组
    for (let i = 0; i < total; i++) {
      const currentTime = new Date(startTime.getTime() + i * 1 * 60 * 1000);
      const timeString = currentTime.toTimeString().slice(0, 8);
      const fixedColor = [1, 4, 7, 11, 14, 17, 21, 24, 27, 30, 33, 37, 40, 43, 47, 50, 53, 57, 60];
      const points: WaterQualityPoint[] = [];
      // 生成第颜色随机一类点位40个
      for (let j = 0; j < 40; j++) {
        const [lng, lat] = lngLat[j];
        points.push({
          id: `type1-${i}-${j}`,
          longitude: lng,
          latitude: lat,
          type: 'type1',
          color: fixedColor.includes(j) ? '#00FF00' : getcolor(total, i, lng),
          value: Math.random() * 100,
          timestamp: timeString,
        });
      }

      //  生成第颜色随机二类点位40个
      for (let j = 40; j < 80; j++) {
        const [lng, lat] = lngLat[j];
        points.push({
          id: `type2-${i}-${j}`,
          longitude: lng,
          latitude: lat,
          type: 'type2',
          color: fixedColor.includes(j) ? '#00FF00' : getcolor(total, i, lng),
          value: Math.random() * 100,
          timestamp: timeString,
        });
      }

      timeSlots.push({
        timestamp: timeString,
        points,
      });
    }

    return timeSlots;
  };
  // 栅格图层
  // function clipLayer(geojson, clipedLayer) {
  //   let clipVectorSource = new VectorSource({
  //     features: new GeoJSON().readFeatures(geojson),
  //   });

  //   let features = clipVectorSource.getFeatures();

  //   let geometry = features[0].getGeometry();

  //   clipedLayer.on('prerender', function (event) {
  //     var ctx = event.context;

  //     var vecCtx = getVectorContext(event);

  //     //这里写transparent是在为被抹去的区域着色，transparent表示透明

  //     //最好不要写其他颜色，因为在预渲染的时候会闪烁一下

  //     vecCtx.setStyle(new Style({ fill: new Fill({ color: 'transparent' }) }));

  //     ctx.save();

  //     vecCtx.drawGeometry(geometry);

  //     ctx.clip();
  //   });

  //   clipedLayer.on('postrender', function (event) {
  //     var ctx = event.context;

  //     ctx.restore();
  //   });
  // }

  // 地图注册回调
  const registerMap = (instance: HMapInstance) => {
    mapInstance.value = instance;

    // 延迟监听地图缩放变化，确保地图完全初始化
    nextTick(() => {
      const map = instance.getMap();
      if (map) {
        const view = map.getView();

        // 初始化当前缩放级别
        currentZoom.value = view.getZoom() || 13;
        // console.log('初始缩放级别:', currentZoom.value);
        // 监听缩放变化
        view.on('change:resolution', () => {
          const newZoom = view.getZoom() || 13;
          // console.log('resolution变化:', currentZoom.value, '->', newZoom);
          currentZoom.value = newZoom;
        });
        // 监听地图移动结束事件（包括缩放）
        map.on('moveend', () => {
          const newZoom = view.getZoom() || 13;
          if (Math.abs(newZoom - currentZoom.value) > 0.1) {
            // console.log('moveend缩放变化:', currentZoom.value, '->', newZoom);
            currentZoom.value = newZoom;
          }
        });
      }
    });
  };

  // 播放控制函数
  const togglePlay = () => {
    if (isPlaying.value) {
      pausePlayback();
    } else {
      if (timeBreak.value <= 0 || timePlayTotal.value <= 0) {
        return createMessage.warning('播放时间和数据总数不能为零');
      }
      startPlayback();
    }
  };

  const startPlayback = () => {
    if (isPlaying.value) return;
    isPlaying.value = true;
    const interval = Math.ceil(
      (timePlayTotal.value * 1000) / (timeBreak.value * playbackSpeed.value),
    ); // 根据播放时间、播放次数、播放速度调整间隔
    // console.log('interval', interval, totalTimeStep.value, timeBreak.value, playbackSpeed.value);
    console.time('计时器1');
    playTimer.value = setInterval(() => {
      if (currentTimeIndex.value >= timeBreak.value - 1) {
        // 播放完毕，重置到开始
        currentTimeIndex.value = 0;
        console.timeEnd('计时器1');
        pausePlayback();
      } else {
        currentTimeIndex.value++;
        console.log(currentTimeIndex.value);
      }
    }, interval);
  };

  const pausePlayback = () => {
    isPlaying.value = false;
    if (playTimer.value) {
      clearInterval(playTimer.value);
      playTimer.value = undefined;
    }
  };

  const resetPlayback = () => {
    pausePlayback();
    timeBreak.value = Math.max(1, 300); // 确保大于0
    timePlayTotal.value = Math.max(1, 10); // 确保大于0
    currentTimeIndex.value = 0;
  };

  // 测试缩放功能
  // const testZoom = () => {
  //   if (mapInstance.value) {
  //     const map = mapInstance.value.getMap();
  //     if (map) {
  //       const view = map.getView();
  //       const currentZoomLevel = view.getZoom() || 13;
  //       // 在10.8和15之间切换缩放级别来测试
  //       const newZoom = currentZoomLevel < 12 ? 15 : 10.8;
  //       view.setZoom(newZoom);
  //     }
  //   }
  // };

  // 进度条变化处理
  const onProgressChange = (value: number) => {
    currentTimeIndex.value = value;
  };

  // 播放速度变化处理
  // const onSpeedChange = () => {
  //   if (isPlaying.value) {
  //     // 重新启动播放以应用新速度
  //     pausePlayback();
  //     startPlayback();
  //   }
  // };

  // 进度条提示格式化
  const formatTooltip = (value: number) => {
    if (allTimeSlots.value.length === 0 || value >= allTimeSlots.value.length) {
      return '00:00:00';
    }
    return allTimeSlots.value[value].timestamp;
  };

  // 组件挂载时初始化数据
  onMounted(() => {
    // 生成模拟数据
    allTimeSlots.value = generateTimeSlotData();
    console.log(`生成了 ${allTimeSlots.value.length} 组水质监测数据`, allTimeSlots.value);

    // 加载深圳市边界数据
    loadShenzhenBoundary();
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (playTimer.value) {
      clearInterval(playTimer.value);
    }
  });

  // 监听播放状态变化
  watch(isPlaying, (newVal) => {
    if (!newVal && playTimer.value) {
      clearInterval(playTimer.value);
      playTimer.value = undefined;
    }
  });

  // 监听缩放级别变化
  watch(currentZoom, (newZoom, oldZoom) => {
    console.log(newZoom, oldZoom);
    // console.log(`缩放级别变化: ${oldZoom} -> ${newZoom}, 显示热力图: ${newZoom < 11}`);
  });
</script>

<style lang="less" scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .controller-content {
      flex-direction: column;
      gap: 15px;
    }

    .progress-container {
      width: 100%;
      margin: 0;
    }

    .data-stats {
      justify-content: center;
      width: 100%;
    }
  }

  @media (max-width: 768px) {
    .playback-controller {
      left: 10px;
      right: 10px;
      bottom: 10px;
      padding: 15px;
    }

    .play-controls {
      justify-content: center;
      width: 100%;
    }

    .time-display {
      width: 100%;
    }

    .speed-controls {
      justify-content: center;
      width: 100%;
    }
  }

  .water-quality-monitoring-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #f0f2f5;
  }

  .map-container {
    width: 100%;
    height: calc(100vh - 50px);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .playback-controller {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .controller-content {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
  }

  .play-controls {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .time-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
  }

  .current-time {
    font-size: 18px;
    font-weight: bold;
    color: #1890ff;
    font-family: 'Courier New', monospace;
  }

  .time-info {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }

  .progress-container {
    flex: 1;
    min-width: 200px;
    margin: 0 20px;
  }

  .progress-slider {
    width: 100%;
  }

  .speed-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
  }

  .cluster-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
  }

  .data-stats {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
  }

  .stat-label {
    color: #666;
  }

  .stat-value {
    font-weight: bold;
    color: #1890ff;
    min-width: 20px;
    text-align: center;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }
  /* 地图点位动画效果 */
  :deep(.ol-viewport) {
    .ol-layer canvas {
      transition: opacity 0.3s ease-in-out;
    }
  }

  /* 自定义滑块样式 */
  :deep(.ant-slider) {
    .ant-slider-rail {
      background-color: #e8e8e8;
      height: 6px;
    }

    .ant-slider-track {
      background: linear-gradient(90deg, #1890ff, #40a9ff);
      height: 6px;
    }

    .ant-slider-handle {
      width: 16px;
      height: 16px;
      border: 3px solid #1890ff;
      background-color: #fff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .ant-slider-handle:hover,
    .ant-slider-handle:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
    }
  }

  /* 按钮样式优化 */
  :deep(.ant-btn) {
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border: none;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
    }

    &.ant-btn-primary:hover {
      background: linear-gradient(135deg, #40a9ff, #1890ff);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
    }
  }
</style>
